# Vidur Validation Framework

This framework validates the accuracy of Vidur LLM inference simulator by comparing its predictions against vLLM ground truth performance.

## Overview

The validation framework implements the following pipeline:

1. **Data Conversion**: Convert trace files to Vidur-compatible format
2. **vLLM Benchmark**: Run vLLM server and collect performance metrics
3. **Vidur Simulation**: Run Vidur simulation with the same workload
4. **Comparison Analysis**: Compare results and calculate error metrics

## Quick Start

### Prerequisites

- Vidur environment set up (see main README)
- vLLM installed in the environment
- Model weights available at configured path

### Run Complete Validation

```bash
# Run the complete validation pipeline
python -m validation.run_validation

# Or run specific steps
python -m validation.run_validation --step convert
python -m validation.run_validation --step vllm
python -m validation.run_validation --step vidur
python -m validation.run_validation --step compare
```

### Configuration

Edit `validation/config.py` to customize:

- Model path and name
- Number of requests
- QPS (queries per second)
- Hardware settings
- Output directories

### Example Configuration

```python
# Model settings
model_path = "/share_data/llm_weights/Meta-Llama-3.1-8B"
model_name = "meta-llama/Meta-Llama-3.1-8B"

# Experiment settings
num_requests = 512
qps = 6.45
max_tokens = 16384

# Hardware settings
device = "a100"
tensor_parallel_size = 1
```

## Individual Components

### Data Converter

Convert trace files to Vidur format:

```bash
python -m validation.data_converter
```

### vLLM Benchmark

Run vLLM performance benchmark:

```bash
python -m validation.vllm_benchmark
```

### Vidur Runner

Run Vidur simulation:

```bash
python -m validation.vidur_runner
```

### Comparison Analyzer

Analyze and compare results:

```bash
python -m validation.comparison_analyzer
```

## Output Structure

```
validation/results/
├── vllm/
│   ├── benchmark_results.json
│   └── validation_data.csv
├── vidur/
│   ├── processed_results.json
│   ├── request_metrics.csv
│   └── plots/
├── comparison/
│   ├── comparison_results.json
│   ├── summary_report.txt
│   ├── detailed_report.txt
│   ├── metrics_comparison.png
│   ├── error_analysis.png
│   └── throughput_comparison.png
└── validation.log
```

## Key Metrics

The framework compares the following metrics:

- **TTFT (Time to First Token)**: Latency to generate first token
- **TPOT (Time Per Output Token)**: Average time per subsequent token
- **E2E Latency**: End-to-end request latency
- **Request Throughput**: Requests processed per second
- **Output Throughput**: Tokens generated per second

## Validation Criteria

Based on the Vidur paper claim of <9% error:

- ✅ **PASS**: Average error < 9%
- ❌ **FAIL**: Average error ≥ 9%

## Troubleshooting

### Common Issues

1. **vLLM Server Startup Issues**
   - Check GPU availability
   - Verify model path exists
   - Ensure sufficient GPU memory

2. **Vidur Simulation Failures**
   - Verify trace file format
   - Check profiling data availability
   - Ensure model is supported

3. **Memory Issues**
   - Reduce number of requests
   - Use smaller model
   - Increase system memory

### Debug Mode

Enable detailed logging:

```bash
export PYTHONPATH=$PYTHONPATH:.
python -m validation.run_validation --step all
```

Check logs in `validation/results/validation.log`

## Advanced Usage

### Custom Trace Files

```bash
python -m validation.run_validation --trace-file /path/to/custom/trace.csv
```

### Custom Parameters

```bash
python -m validation.run_validation \
    --num-requests 1000 \
    --qps 10.0 \
    --model-path /path/to/model
```

### Skip vLLM Server Startup

If vLLM server is already running:

```bash
python -m validation.run_validation --skip-vllm-server
```

## Results Interpretation

### Summary Report

The summary report shows:
- Overall validation status (PASS/FAIL)
- Error percentages for each metric
- Average error across all metrics

### Detailed Report

The detailed report includes:
- Configuration used
- Raw vLLM and Vidur results
- Detailed error analysis
- Recommendations

### Plots

Generated plots show:
- Side-by-side metric comparisons
- Error percentage analysis
- Throughput comparisons

## Contributing

To extend the validation framework:

1. Add new metrics in `comparison_analyzer.py`
2. Extend configuration in `config.py`
3. Add new analysis methods as needed
4. Update documentation

## References

- [Vidur Paper](https://arxiv.org/abs/2405.05465)
- [vLLM Documentation](https://docs.vllm.ai/)
- [Original Vidur Repository](https://github.com/microsoft/vidur)
