#!/bin/bash

# Vidur LLM Inference Simulation Script
# This script runs a vidur simulation with the configuration from the official documentation
# 
# Configuration:
# - Device: A100 GPU
# - Model: Meta-Llama-3-8B
# - Replicas: 1
# - Tensor Parallel Size: 1
# - Pipeline Stages: 1
# - Request Generator: Synthetic (512 requests)
# - Length Generator: Trace-based with splitwise_conv.csv
# - Interval Generator: Poisson with 6.45 QPS
# - Scheduler: <PERSON><PERSON> with 512 batch size cap and 512 chunk size

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "vidur/main.py" ]; then
    print_error "vidur/main.py not found. Please run this script from the vidur repository root directory."
    exit 1
fi

# Check if the trace file exists
TRACE_FILE="./data/processed_traces/splitwise_conv.csv"
if [ ! -f "$TRACE_FILE" ]; then
    print_warning "Trace file $TRACE_FILE not found. The simulation may fail."
    print_warning "Please ensure you have the required trace data in the data/processed_traces/ directory."
fi

# Check if Python environment is activated
if [ -z "$VIRTUAL_ENV" ] && [ -z "$CONDA_DEFAULT_ENV" ]; then
    print_warning "No virtual environment detected. Make sure you have activated your Python environment."
    print_warning "You can activate it using:"
    print_warning "  - For conda: conda activate ./env"
    print_warning "  - For venv: source .venv/bin/activate"
fi

print_info "Starting Vidur LLM Inference Simulation..."
print_info "Configuration:"
print_info "  - Device: A100"
print_info "  - Model: meta-llama/Meta-Llama-3-8B"
print_info "  - Replicas: 1"
print_info "  - Requests: 512"
print_info "  - QPS: 6.45"
print_info "  - Scheduler: Sarathi"

# Run the simulation
python -m vidur.main \
    --replica_config_device a100 \
    --replica_config_model_name meta-llama/Meta-Llama-3-8B \
    --cluster_config_num_replicas 1 \
    --replica_config_tensor_parallel_size 1 \
    --replica_config_num_pipeline_stages 1 \
    --request_generator_config_type synthetic \
    --synthetic_request_generator_config_num_requests 512 \
    --length_generator_config_type trace \
    --trace_request_length_generator_config_max_tokens 16384 \
    --trace_request_length_generator_config_trace_file ./data/processed_traces/splitwise_conv.csv \
    --interval_generator_config_type poisson \
    --poisson_request_interval_generator_config_qps 6.45 \
    --replica_scheduler_config_type sarathi \
    --sarathi_scheduler_config_batch_size_cap 512 \
    --sarathi_scheduler_config_chunk_size 512 \
    --random_forrest_execution_time_predictor_config_prediction_max_prefill_chunk_size 16384 \
    --random_forrest_execution_time_predictor_config_prediction_max_batch_size 512 \
    --random_forrest_execution_time_predictor_config_prediction_max_tokens_per_request 16384

# Check if simulation completed successfully
if [ $? -eq 0 ]; then
    print_info "Simulation completed successfully!"
    print_info "Results should be available in the simulator_output/ directory."
    
    # Find the most recent output directory
    LATEST_OUTPUT=$(ls -t simulator_output/ 2>/dev/null | head -n1)
    if [ -n "$LATEST_OUTPUT" ]; then
        print_info "Latest output directory: simulator_output/$LATEST_OUTPUT"
    fi
else
    print_error "Simulation failed with exit code $?"
    exit 1
fi
