# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

Vidur is a high-fidelity and extensible LLM inference system simulator built in Python. It simulates LLM inference clusters to study system performance, capacity planning, and test new research ideas without requiring GPU access beyond initial profiling.

## Development Commands

### Environment Setup
- **Recommended**: `mamba env create -p ./env -f ./environment.yml && mamba env update -f environment-dev.yml`
- **Alternative**: `python3.10 -m venv .venv && source .venv/bin/activate && pip install -r requirements.txt`

### Code Quality
- **Format code**: `make format` (runs isort + black)
- **Lint code**: `make lint` (runs black --check + isort --check)
- **Individual tools**:
  - `make format/black` - Format with black
  - `make format/isort` - Sort imports
  - `make lint/black` - Check black formatting
  - `make lint/isort` - Check import sorting

### Running Simulations
- **Basic**: `python -m vidur.main`
- **With parameters**: `python -m vidur.main --replica_config_device a100 --replica_config_model_name meta-llama/Meta-Llama-3-8B --cluster_config_num_replicas 1`
- **Help**: `python -m vidur.main -h`

## Architecture

### Core Components
- **Simulator** (`vidur/simulator.py`): Main discrete event simulator with event queue
- **Config System** (`vidur/config/`): Hierarchical configuration with CLI argument parsing via `SimulationConfig.create_from_cli_args()`
- **Entities** (`vidur/entities/`): Core objects (Cluster, Replica, Request, Batch, BatchStage)
- **Events** (`vidur/events/`): Event-driven system (RequestArrival, BatchEnd, BatchStageEnd, etc.)
- **Schedulers**:
  - **Global Scheduler** (`vidur/scheduler/global_scheduler/`): Routes requests to replicas (Round Robin, Random, LoR)
  - **Replica Scheduler** (`vidur/scheduler/replica_scheduler/`): Manages batching within replicas (vLLM, Sarathi, Orca, etc.)

### Key Design Patterns
- **Registry Pattern**: Used throughout for pluggable components (schedulers, predictors, generators)
- **Event-Driven Simulation**: Discrete event simulation with priority queue
- **Hierarchical Configuration**: Nested dataclass configs with CLI generation
- **Execution Time Prediction**: ML models (Random Forest, Linear Regression) predict GPU execution times

### Data Flow
1. **Request Generation**: Synthetic or trace-based request arrival
2. **Global Scheduling**: Routes requests to replicas based on policy
3. **Replica Scheduling**: Batches requests within replicas using specific algorithms
4. **Execution Time Prediction**: Predicts compute times for attention/MLP operations
5. **Metrics Collection**: Tracks TTFT, TPOT, throughput, etc.

### Profiling System
- Pre-computed execution time data in `data/profiling/` for different GPU SKUs and models
- Separate profiling tools in `vidur/profiling/` for attention, MLP, and collectives
- Supports A100, H100, A40 with various parallelism configurations

### Configuration Architecture
- Base classes in `vidur/config/`: `BasePolyConfig` for polymorphic configs
- Device/model configs specify hardware and model parameters
- Scheduler configs define batching and scheduling behavior
- All configs auto-generate CLI arguments and validation

### Output and Metrics
- Results saved to `simulator_output/<TIMESTAMP>/`
- Chrome trace generation for visualization
- WandB integration for experiment tracking
- Comprehensive metrics in `vidur/metrics/`

## Supported Models and Hardware
- Models: Llama-2/3, CodeLlama, InternLM, Qwen (see README for full matrix)
- Hardware: A100 80GB, H100, A40 with DGX and pairwise NVLink configurations
- Parallelism: Tensor Parallel (TP) and Pipeline Parallel (PP) support